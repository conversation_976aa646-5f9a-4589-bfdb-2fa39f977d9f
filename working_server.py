#!/usr/bin/env python3
"""
Adaptive Meeting Twin - Enhanced Signaling Server
A Flask-SocketIO server for WebRTC signaling with AI features
"""

import sys
import os
import json
import threading
import time
import socket
from datetime import datetime

# Add some debug output
print("=" * 50)
print("Adaptive Meeting Twin - Enhanced Signaling Server")
print("=" * 50)
print(f"Python version: {sys.version}")
print(f"Working directory: {os.getcwd()}")

try:
    from flask import Flask, request, jsonify
    print("✓ Flask imported successfully")

    from flask_socketio import SocketIO, join_room, emit
    print("✓ Flask-SocketIO imported successfully")

    # AI and ML imports
    import google.generativeai as genai
    print("✓ Google Generative AI imported successfully")

    import nltk
    from nltk.sentiment.vader import SentimentIntensityAnalyzer
    print("✓ NLTK imported successfully")

    import numpy as np
    from scipy.interpolate import make_interp_spline
    print("✓ NumPy and SciPy imported successfully")

    # Computer vision imports for attention tracking
    import cv2
    import mediapipe as mp
    from collections import deque
    print("✓ OpenCV and MediaPipe imported successfully")

    # Import attention tracking service
    from attention_service import get_attention_tracker
    print("✓ Attention tracking service imported successfully")

except ImportError as e:
    print(f"✗ Import error: {e}")
    print("Please install missing dependencies:")
    print("pip install google-generativeai nltk scipy opencv-python mediapipe")
    sys.exit(1)

# Initialize AI components
print("\n--- Initializing AI Components ---")

# Configure Gemini AI
API_KEY = "AIzaSyDD3-4VGqZbncObJ7_VFA2UeovjrEO6ag0"  # ⚠️ Demo key
genai.configure(api_key=API_KEY)
model = genai.GenerativeModel("gemini-1.5-flash")
print("✓ Gemini AI configured")

# Initialize NLTK components
try:
    nltk.download('vader_lexicon', quiet=True)
    nltk.download('punkt', quiet=True)
    sia = SentimentIntensityAnalyzer()
    print("✓ NLTK sentiment analyzer ready")
except Exception as e:
    print(f"⚠️ NLTK setup warning: {e}")
    sia = None

# Initialize MediaPipe for attention tracking
mp_face_mesh = mp.solutions.face_mesh
mp_drawing = mp.solutions.drawing_utils
print("✓ MediaPipe face mesh initialized")

# Attention tracking configuration
CLOSE_EYE_THRESHOLD = 0.22
FPS = 30
LEFT_EYE_INDICES = [33, 160, 158, 133, 153, 144]
RIGHT_EYE_INDICES = [362, 385, 387, 263, 373, 380]
LEFT_IRIS = [468, 469, 470, 471]
RIGHT_IRIS = [473, 474, 475, 476]

# Global variables for attention tracking
attention_sessions = {}  # room_id -> attention_data
sentiment_sessions = {}  # room_id -> sentiment_data

print("✓ AI components initialized successfully")

# Create Flask app with static file serving
app = Flask(__name__, static_folder='frontend', static_url_path='')
app.config['SECRET_KEY'] = 'adaptive-meeting-twin-secret'
print("✓ Flask app created with frontend static serving")

# Initialize SocketIO
socketio = SocketIO(app, cors_allowed_origins="*", logger=True, engineio_logger=True)
print("✓ SocketIO initialized with CORS enabled")

# AI Service Functions
def summarize_and_extract(transcript):
    """Generate AI summary using Gemini"""
    try:
        prompt = f"""
        You are an AI meeting assistant. Analyze the following transcript and produce:
        1. A concise meeting summary (3-5 sentences).
        2. Clear meeting minutes (MOM).
        3. Action items with assignees if mentioned.

        Transcript:
        {transcript}

        Format the response as:
        📋 Summary:
        ...

        📝 Meeting Minutes:
        - Point 1
        - Point 2

        ✅ Action Items:
        - Task 1
        - Task 2
        """

        response = model.generate_content(prompt)
        return {"success": True, "summary": response.text}
    except Exception as e:
        return {"success": False, "error": str(e)}

def analyze_sentiment_advanced(text, window=5):
    """Advanced sentiment analysis with smooth interpolation"""
    if not sia:
        return {"success": False, "error": "NLTK not available"}

    try:
        words = nltk.word_tokenize(text)
        scores = []
        chunks = []

        for i in range(0, len(words), window):
            chunk = " ".join(words[i:i+window])
            score = sia.polarity_scores(chunk)['compound']
            scores.append(score)
            chunks.append(chunk)

        # Create smooth interpolation if enough points
        if len(scores) > 3:
            x = np.arange(len(scores))
            y = np.array(scores)
            X_ = np.linspace(x.min(), x.max(), min(300, len(scores) * 10))
            spl = make_interp_spline(x, y, k=3)
            Y_ = spl(X_)
            smooth_data = {"x": X_.tolist(), "y": Y_.tolist()}
        else:
            smooth_data = {"x": list(range(len(scores))), "y": scores}

        return {
            "success": True,
            "scores": scores,
            "chunks": chunks,
            "smooth_data": smooth_data,
            "overall_sentiment": np.mean(scores) if scores else 0
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

def eye_aspect_ratio(landmarks, indices, w, h):
    """Calculate eye aspect ratio for attention tracking"""
    pts = [(int(landmarks[i].x * w), int(landmarks[i].y * h)) for i in indices]
    A = np.linalg.norm(np.array(pts[1]) - np.array(pts[5]))
    B = np.linalg.norm(np.array(pts[2]) - np.array(pts[4]))
    C = np.linalg.norm(np.array(pts[0]) - np.array(pts[3]))
    ear = (A + B) / (2.0 * C)
    return ear

def get_local_ip():
    """Get the local IP address for network access"""
    try:
        # Connect to a remote address to determine local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

def find_available_port(start_port=6000, max_attempts=10):
    """Find an available port starting from start_port"""
    for port in range(start_port, start_port + max_attempts):
        try:
            # Test if port is available
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            test_socket.bind(('localhost', port))
            test_socket.close()
            return port
        except OSError:
            continue
    return None

def kill_process_on_port(port):
    """Kill any process using the specified port (Windows)"""
    try:
        import subprocess
        # Find process using the port
        result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
        lines = result.stdout.split('\n')

        for line in lines:
            if f':{port}' in line and 'LISTENING' in line:
                parts = line.split()
                if len(parts) >= 5:
                    pid = parts[-1]
                    print(f"Found process {pid} using port {port}, attempting to terminate...")
                    subprocess.run(['taskkill', '/F', '/PID', pid], capture_output=True)
                    return True
        return False
    except Exception as e:
        print(f"Could not kill process on port {port}: {e}")
        return False

# Flask Routes
@app.route("/")
def home():
    """Serve the main frontend application"""
    return app.send_static_file('index.html')

@app.route("/status")
def status():
    """Server status and access information"""
    local_ip = get_local_ip()
    return {
        "status": "running",
        "message": "Enhanced signaling server is operational",
        "ai_features": True,
        "access": {
            "localhost": "http://localhost:6000",
            "network": f"http://{local_ip}:6000",
            "websocket": f"ws://{local_ip}:6000/socket.io/"
        }
    }

@app.route("/info")
def info():
    """Server information page"""
    local_ip = get_local_ip()
    return f"""
    <h1>🚀 Adaptive Meeting Twin - Enhanced Server</h1>
    <p><strong>Status:</strong> ✅ Running with AI features!</p>

    <h2>🌐 Access URLs:</h2>
    <ul>
        <li><strong>Localhost:</strong> <a href="http://localhost:6000">http://localhost:6000</a></li>
        <li><strong>Network:</strong> <a href="http://{local_ip}:6000">http://{local_ip}:6000</a></li>
        <li><strong>WebSocket:</strong> <code>ws://{local_ip}:6000/socket.io/</code></li>
    </ul>

    <h2>🤖 Available AI Endpoints:</h2>
    <ul>
        <li><code>POST /api/summarize</code> - Generate AI meeting summary</li>
        <li><code>POST /api/sentiment</code> - Advanced sentiment analysis</li>
        <li><code>POST /api/attention/start</code> - Start attention tracking</li>
        <li><code>POST /api/attention/stop</code> - Stop attention tracking</li>
    </ul>

    <p><em>Share the network URL with others to join from different devices!</em></p>
    """



@app.route("/api/summarize", methods=["POST"])
def api_summarize():
    """API endpoint for AI summary generation"""
    try:
        data = request.get_json()
        transcript = data.get("transcript", "")

        if not transcript:
            return jsonify({"success": False, "error": "No transcript provided"}), 400

        result = summarize_and_extract(transcript)
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route("/api/sentiment", methods=["POST"])
def api_sentiment():
    """API endpoint for advanced sentiment analysis"""
    try:
        data = request.get_json()
        text = data.get("text", "")
        window = data.get("window", 5)

        if not text:
            return jsonify({"success": False, "error": "No text provided"}), 400

        result = analyze_sentiment_advanced(text, window)
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route("/api/attention/start", methods=["POST"])
def api_attention_start():
    """API endpoint to start attention tracking for a room"""
    try:
        data = request.get_json()
        room = data.get("room", "default")

        if room not in attention_sessions:
            attention_sessions[room] = {
                "active": True,
                "scores": deque(maxlen=100),
                "timestamps": deque(maxlen=100),
                "ear_history": deque(maxlen=5),
                "att_history": deque(maxlen=10)
            }

        attention_sessions[room]["active"] = True
        return jsonify({"success": True, "message": f"Attention tracking started for room {room}"})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route("/api/attention/stop", methods=["POST"])
def api_attention_stop():
    """API endpoint to stop attention tracking for a room"""
    try:
        data = request.get_json()
        room = data.get("room", "default")

        if room in attention_sessions:
            attention_sessions[room]["active"] = False

        # Stop the attention tracker
        tracker = get_attention_tracker()
        tracker.stop_tracking()

        return jsonify({"success": True, "message": f"Attention tracking stopped for room {room}"})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route("/api/attention/analyze", methods=["POST"])
def api_attention_analyze():
    """API endpoint to analyze a frame for attention metrics"""
    try:
        data = request.get_json()
        base64_frame = data.get("frame", "")
        room = data.get("room", "default")

        if not base64_frame:
            return jsonify({"success": False, "error": "No frame provided"}), 400

        # Get attention tracker and analyze frame
        tracker = get_attention_tracker()
        if not tracker.is_tracking:
            tracker.start_tracking()

        result = tracker.process_base64_frame(base64_frame)

        if "error" in result:
            return jsonify({"success": False, "error": result["error"]}), 400

        # Store in room session if tracking is active
        if room in attention_sessions and attention_sessions[room]["active"]:
            attention_sessions[room]["scores"].append(result["attention_score"])
            attention_sessions[room]["timestamps"].append(result["timestamp"])

        return jsonify({"success": True, "data": result})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

# WebSocket Event Handlers
@socketio.on("connect")
def on_connect():
    print(f"Client connected: {request.sid}")
    emit("connected", {"message": "Connected to enhanced signaling server"})

@socketio.on("disconnect")
def on_disconnect():
    print(f"Client disconnected: {request.sid}")

@socketio.on("join")
def on_join(data):
    room = data.get("room")
    if not room:
        emit("error", {"message": "Room name is required"})
        return

    join_room(room)
    print(f"Client {request.sid} joined room: {room}")

    # Initialize room data if not exists
    if room not in sentiment_sessions:
        sentiment_sessions[room] = {"data": [], "active": True}
    if room not in attention_sessions:
        attention_sessions[room] = {
            "active": False,
            "scores": deque(maxlen=100),
            "timestamps": deque(maxlen=100),
            "ear_history": deque(maxlen=5),
            "att_history": deque(maxlen=10)
        }

    # Send joined confirmation to the joining client
    emit("joined", {"room": room, "id": request.sid})

    # Notify other peers in the room about the new peer
    emit("peer-joined", {"id": request.sid}, room=room, include_self=False)

    print(f"✅ Client {request.sid} successfully joined room {room}")

@socketio.on("signal")
def on_signal(data):
    room = data.get("room")
    signal_data = data.get("data")

    if not room or not signal_data:
        emit("error", {"message": "Room and signal data are required"})
        return

    print(f"Relaying signal from {request.sid} in room {room}")
    emit("signal", {"id": request.sid, "data": signal_data}, room=room, include_self=False)

@socketio.on("sentiment_analysis")
def on_sentiment_analysis(data):
    """Handle real-time sentiment analysis requests"""
    room = data.get("room")
    text = data.get("text", "")

    if not room or not text:
        emit("error", {"message": "Room and text are required"})
        return

    result = analyze_sentiment_advanced(text)
    if result["success"]:
        # Store in room session
        if room in sentiment_sessions:
            sentiment_sessions[room]["data"].append({
                "timestamp": time.time(),
                "text": text,
                "sentiment": result["overall_sentiment"],
                "detailed": result
            })

        # Broadcast to room
        emit("sentiment_result", result, room=room)
    else:
        emit("sentiment_error", result, room=room)

@socketio.on("attention_data")
def on_attention_data(data):
    """Handle attention tracking data from frontend"""
    room = data.get("room")
    attention_score = data.get("attention_score", 0)

    if not room:
        emit("error", {"message": "Room is required"})
        return

    if room in attention_sessions and attention_sessions[room]["active"]:
        current_time = time.time()
        attention_sessions[room]["scores"].append(attention_score)
        attention_sessions[room]["timestamps"].append(current_time)

        # Broadcast to room
        emit("attention_update", {
            "attention_score": attention_score,
            "timestamp": current_time
        }, room=room)

if __name__ == "__main__":
    local_ip = get_local_ip()

    # Handle port conflicts
    port = 6000
    print("\n" + "=" * 70)
    print("🚀 Starting Enhanced Adaptive Meeting Twin Server...")
    print("=" * 70)

    # Check if port is in use and try to resolve
    available_port = find_available_port(port)
    if available_port != port:
        print(f"⚠️  Port {port} is in use. Trying to free it...")
        if kill_process_on_port(port):
            print(f"✅ Freed port {port}")
            available_port = port
        elif available_port:
            print(f"✅ Using alternative port {available_port}")
            port = available_port
        else:
            print("❌ No available ports found. Please close other applications using ports 6000-6010")
            input("Press Enter to exit...")
            sys.exit(1)

    print("📍 Access URLs:")
    print(f"   Localhost: http://localhost:{port}")
    print(f"   Network:   http://{local_ip}:{port}")
    print("🤖 AI Features: ✓ Gemini Summary ✓ VADER Sentiment ✓ Attention Tracking")
    print("🌐 Frontend: Served directly from server (no file:// needed)")
    print("📱 Share network URL for multi-device access")
    print("Press Ctrl+C to stop the server")
    print("=" * 70 + "\n")

    try:
        socketio.run(
            app,
            host="0.0.0.0",
            port=port,
            debug=False,  # Disable debug to avoid reloader issues
            allow_unsafe_werkzeug=True,
            use_reloader=False
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        if "10048" in str(e) or "Address already in use" in str(e):
            print("🔧 Port conflict detected. Trying to resolve...")
            if kill_process_on_port(port):
                print("✅ Port freed. Please restart the server.")
            else:
                print("❌ Could not free port. Please manually close applications using the port.")
                print("💡 Try running: netstat -ano | findstr :6000")
                print("💡 Then: taskkill /F /PID <process_id>")
        else:
            import traceback
            traceback.print_exc()

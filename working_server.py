#!/usr/bin/env python3
"""
Adaptive Meeting Twin - Signaling Server
A Flask-SocketIO server for WebRTC signaling
"""

import sys
import os

# Add some debug output
print("=" * 50)
print("Adaptive Meeting Twin - Signaling Server")
print("=" * 50)
print(f"Python version: {sys.version}")
print(f"Working directory: {os.getcwd()}")

try:
    from flask import Flask, request
    print("✓ Flask imported successfully")
    
    from flask_socketio import SocketIO, join_room, emit
    print("✓ Flask-SocketIO imported successfully")
    
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'adaptive-meeting-twin-secret'
print("✓ Flask app created")

# Initialize SocketIO
socketio = SocketIO(app, cors_allowed_origins="*", logger=True, engineio_logger=True)
print("✓ SocketIO initialized with CORS enabled")

@app.route("/")
def home():
    return """
    <h1>Adaptive Meeting Twin - Signaling Server</h1>
    <p>Server is running successfully!</p>
    <p>Frontend should connect to: <code>http://localhost:6000</code></p>
    <p>WebSocket endpoint: <code>ws://localhost:6000/socket.io/</code></p>
    """

@app.route("/status")
def status():
    return {"status": "running", "message": "Signaling server is operational"}

@socketio.on("connect")
def on_connect():
    print(f"Client connected: {request.sid}")
    emit("connected", {"message": "Connected to signaling server"})

@socketio.on("disconnect")
def on_disconnect():
    print(f"Client disconnected: {request.sid}")

@socketio.on("join")
def on_join(data):
    room = data.get("room")
    if not room:
        emit("error", {"message": "Room name is required"})
        return
    
    join_room(room)
    print(f"Client {request.sid} joined room: {room}")
    emit("joined", {"room": room, "id": request.sid})
    emit("peer-joined", {"id": request.sid}, room=room, include_self=False)

@socketio.on("signal")
def on_signal(data):
    room = data.get("room")
    signal_data = data.get("data")
    
    if not room or not signal_data:
        emit("error", {"message": "Room and signal data are required"})
        return
    
    print(f"Relaying signal from {request.sid} in room {room}")
    emit("signal", {"id": request.sid, "data": signal_data}, room=room, include_self=False)

if __name__ == "__main__":
    print("\n" + "=" * 50)
    print("Starting server...")
    print("Server will be available at: http://localhost:6000")
    print("Press Ctrl+C to stop the server")
    print("=" * 50 + "\n")
    
    try:
        socketio.run(
            app, 
            host="0.0.0.0", 
            port=6000, 
            debug=True, 
            allow_unsafe_werkzeug=True,
            use_reloader=False  # Disable reloader to avoid issues
        )
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Server error: {e}")
        import traceback
        traceback.print_exc()

@echo off
echo ========================================
echo Port 6000 Cleanup Utility
echo ========================================
echo.

echo Checking for processes using port 6000...
netstat -ano | findstr :6000

echo.
echo Finding process ID using port 6000...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :6000 ^| findstr LISTENING') do (
    echo Found process ID: %%a
    echo Attempting to terminate process %%a...
    taskkill /F /PID %%a
    if errorlevel 1 (
        echo Failed to terminate process %%a
    ) else (
        echo Successfully terminated process %%a
    )
)

echo.
echo Verifying port 6000 is now free...
netstat -ano | findstr :6000
if errorlevel 1 (
    echo ✅ Port 6000 is now free!
) else (
    echo ⚠️ Port 6000 may still be in use
)

echo.
echo ========================================
echo Cleanup completed
echo ========================================
pause

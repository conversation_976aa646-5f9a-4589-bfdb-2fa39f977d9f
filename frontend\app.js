// Adaptive Meeting Twin - Complete Implementation
class AdaptiveMeetingTwin {
  constructor() {
    // Auto-detect server port from current URL or default to 6000
    const currentPort = window.location.port || '6000';
    const serverUrl = `${window.location.protocol}//${window.location.hostname}:${currentPort}`;

    this.socket = io(serverUrl);
    this.serverUrl = serverUrl;
    console.log('🌐 Connecting to server:', serverUrl);
    this.pc = null;
    this.localStream = null;
    this.remoteStream = null;
    this.currentRoom = null;
    this.isConnected = false;
    this.isFirstPeer = false;

    // Adaptive fallback states
    this.networkMode = 'normal'; // normal, degraded, audio-only, transcript-only
    this.stats = { rtt: 0, videoLoss: 0, audioLoss: 0, outboundBitrate: 0 };

    // Transcription
    this.recognition = null;
    this.transcript = [];
    this.isTranscribing = false;

    // Engagement tracking
    this.engagement = {
      participants: new Map(),
      startTime: null,
      speakingTimes: new Map()
    };

    // Recording
    this.mediaRecorder = null;
    this.recordedChunks = [];
    this.isRecording = false;

    // Sentiment data
    this.sentimentData = [];
    this.sentimentChart = null;

    // Attention tracking
    this.attentionData = [];
    this.attentionChart = null;
    this.isAttentionTracking = false;
    this.attentionInterval = null;

    this.initializeUI();
    this.setupSocketListeners();
  }

  initializeUI() {
    // Get UI elements
    this.elements = {
      roomInput: document.getElementById('roomInput'),
      btnJoin: document.getElementById('btnJoin'),
      btnLeave: document.getElementById('btnLeave'),
      connectionStatus: document.getElementById('connectionStatus'),
      localVideo: document.getElementById('localVideo'),
      remoteVideo: document.getElementById('remoteVideo'),
      netMode: document.getElementById('netMode'),
      stats: document.getElementById('stats'),
      transcript: document.getElementById('transcript'),
      btnSummarize: document.getElementById('btnSummarize'),
      mom: document.getElementById('mom'),
      actions: document.getElementById('actions'),
      engBars: document.getElementById('engBars'),
      champ: document.getElementById('champ'),
      silent: document.getElementById('silent'),
      sentChart: document.getElementById('sentChart'),
      currentAttention: document.getElementById('currentAttention'),
      attentionAlert: document.getElementById('attentionAlert'),
      btnRecStart: document.getElementById('btnRecStart'),
      btnRecStop: document.getElementById('btnRecStop'),
      playback: document.getElementById('playback')
    };

    // Setup event listeners
    this.elements.btnJoin.addEventListener('click', () => this.joinRoom());
    this.elements.btnLeave.addEventListener('click', () => this.leaveRoom());
    this.elements.btnSummarize.addEventListener('click', () => this.generateMOM());
    this.elements.btnRecStart.addEventListener('click', () => this.startRecording());
    this.elements.btnRecStop.addEventListener('click', () => this.stopRecording());

    // Initialize charts
    this.initializeSentimentChart();
  }

  setupSocketListeners() {
    this.socket.on('connect', () => {
      console.log('Connected to signaling server');
      this.updateConnectionStatus('Connected to server');
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from signaling server');
      this.updateConnectionStatus('Disconnected');
    });

    this.socket.on('joined', (data) => {
      console.log('Joined room:', data.room);
      this.mySocketId = data.id;
    });

    this.socket.on('peer-joined', (data) => {
      console.log('Peer joined:', data.id);
      // Only create offer if our socket ID is lexicographically smaller
      // This ensures only one peer creates the offer, preventing both from being "local"
      if (this.mySocketId && this.mySocketId < data.id) {
        console.log('Creating offer as initiating peer (ID:', this.mySocketId, '< ID:', data.id, ')');
        setTimeout(() => this.createOffer(), 1000); // Small delay to ensure both peers are ready
      } else {
        console.log('Waiting for offer from peer (ID:', this.mySocketId, '>= ID:', data.id, ')');
        this.isFirstPeer = false;
      }
    });

    this.socket.on('signal', async (data) => {
      await this.handleSignalingMessage(data);
    });
  }

  async joinRoom() {
    const room = this.elements.roomInput.value.trim();
    if (!room) {
      alert('Please enter a room name');
      return;
    }

    try {
      this.currentRoom = room;
      this.updateConnectionStatus('Joining room...');

      // Get user media
      this.localStream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });
      this.elements.localVideo.srcObject = this.localStream;

      // Create peer connection
      await this.createPeerConnection();

      // Join room via socket
      this.socket.emit('join', { room });

      // We'll determine if we're first peer based on socket ID comparison
      this.isFirstPeer = true;

      this.updateConnectionStatus('In room: ' + room);
      this.elements.btnJoin.disabled = true;
      this.elements.btnLeave.disabled = false;
      this.elements.roomInput.disabled = true;

      // Start engagement tracking
      this.startEngagementTracking();

      // Start automatic transcription
      this.startTranscription();

      // Start automatic attention tracking
      this.startAttentionTracking();

    } catch (error) {
      console.error('Error joining room:', error);
      this.updateConnectionStatus('Error joining room');
      alert('Error accessing camera/microphone: ' + error.message);
    }
  }

  async createPeerConnection() {
    const config = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
      ]
    };

    this.pc = new RTCPeerConnection(config);

    // Add local tracks
    this.localStream.getTracks().forEach(track => {
      this.pc.addTrack(track, this.localStream);
    });

    // Handle remote stream
    this.remoteStream = new MediaStream();
    this.elements.remoteVideo.srcObject = this.remoteStream;

    this.pc.ontrack = (event) => {
      event.streams[0].getTracks().forEach(track => {
        this.remoteStream.addTrack(track);
      });
    };

    // Handle ICE candidates
    this.pc.onicecandidate = (event) => {
      if (event.candidate) {
        this.socket.emit('signal', {
          room: this.currentRoom,
          data: { candidate: event.candidate }
        });
      }
    };

    // Connection state monitoring
    this.pc.onconnectionstatechange = () => {
      console.log('Connection state:', this.pc.connectionState);
      this.updateConnectionStatus('Connection: ' + this.pc.connectionState);

      if (this.pc.connectionState === 'connected') {
        this.isConnected = true;
        this.startNetworkMonitoring();
      } else if (this.pc.connectionState === 'disconnected' ||
                 this.pc.connectionState === 'failed') {
        this.isConnected = false;
      }
    };
  }

  async createOffer() {
    if (!this.pc) {
      console.error('No peer connection available for offer creation');
      return;
    }

    try {
      console.log('Creating WebRTC offer...');
      const offer = await this.pc.createOffer();
      await this.pc.setLocalDescription(offer);

      this.socket.emit('signal', {
        room: this.currentRoom,
        data: { sdp: offer }
      });

      console.log('✅ Offer created and sent to room:', this.currentRoom);
    } catch (error) {
      console.error('❌ Error creating offer:', error);
    }
  }

  async handleSignalingMessage(data) {
    if (!this.pc) {
      console.error('No peer connection available for signaling');
      return;
    }

    try {
      if (data.data.sdp) {
        console.log('📨 Received SDP:', data.data.sdp.type, 'from peer:', data.id);
        await this.pc.setRemoteDescription(new RTCSessionDescription(data.data.sdp));

        if (data.data.sdp.type === 'offer') {
          // We received an offer, so we're not the first peer
          this.isFirstPeer = false;
          console.log('📞 Creating answer for received offer...');

          const answer = await this.pc.createAnswer();
          await this.pc.setLocalDescription(answer);

          this.socket.emit('signal', {
            room: this.currentRoom,
            data: { sdp: answer }
          });

          console.log('✅ Answer created and sent');
        } else if (data.data.sdp.type === 'answer') {
          console.log('✅ Received answer, connection should be established');
        }
      } else if (data.data.candidate) {
        console.log('🧊 Received ICE candidate from peer:', data.id);
        await this.pc.addIceCandidate(new RTCIceCandidate(data.data.candidate));
      }
    } catch (error) {
      console.error('❌ Error handling signaling message:', error);
    }
  }

  leaveRoom() {
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    if (this.pc) {
      this.pc.close();
      this.pc = null;
    }

    if (this.mediaRecorder && this.isRecording) {
      this.stopRecording();
    }

    this.stopTranscription();
    this.currentRoom = null;
    this.isConnected = false;
    this.isFirstPeer = false;

    this.elements.localVideo.srcObject = null;
    this.elements.remoteVideo.srcObject = null;
    this.elements.btnJoin.disabled = false;
    this.elements.btnLeave.disabled = true;
    this.elements.roomInput.disabled = false;

    this.updateConnectionStatus('Disconnected');
    this.updateNetworkMode('normal');
  }

  updateConnectionStatus(status) {
    this.elements.connectionStatus.textContent = status;
  }

  updateNetworkMode(mode) {
    this.networkMode = mode;
    this.elements.netMode.textContent = `Mode: ${mode.charAt(0).toUpperCase() + mode.slice(1)}`;
  }

  // Network monitoring and adaptive fallback
  startNetworkMonitoring() {
    setInterval(async () => {
      if (!this.pc || !this.isConnected) return;

      try {
        const stats = await this.pc.getStats();
        this.analyzeStats(stats);
        this.updateStatsDisplay();
        this.adaptToNetworkConditions();
      } catch (error) {
        console.error('Error getting stats:', error);
      }
    }, 2000);
  }

  analyzeStats(stats) {
    let videoLoss = 0, audioLoss = 0, rtt = 0, outboundBitrate = 0;

    stats.forEach(report => {
      if (report.type === 'inbound-rtp') {
        if (report.kind === 'video') {
          videoLoss = report.packetsLost || 0;
        } else if (report.kind === 'audio') {
          audioLoss = report.packetsLost || 0;
        }
      } else if (report.type === 'candidate-pair' && report.state === 'succeeded') {
        rtt = report.currentRoundTripTime * 1000 || 0;
      } else if (report.type === 'outbound-rtp') {
        outboundBitrate += report.bytesSent || 0;
      }
    });

    this.stats = { rtt, videoLoss, audioLoss, outboundBitrate };
  }

  updateStatsDisplay() {
    const { rtt, videoLoss, audioLoss, outboundBitrate } = this.stats;
    this.elements.stats.textContent =
      `RTT: ${Math.round(rtt)}ms | Loss(v/a): ${videoLoss} / ${audioLoss} | Out: ${Math.round(outboundBitrate/1000)} kbps`;
  }

  adaptToNetworkConditions() {
    const { rtt, videoLoss, audioLoss } = this.stats;

    if (rtt > 500 || videoLoss > 50) {
      if (this.networkMode !== 'transcript-only') {
        this.fallbackToTranscriptOnly();
      }
    } else if (rtt > 300 || videoLoss > 20) {
      if (this.networkMode !== 'audio-only') {
        this.fallbackToAudioOnly();
      }
    } else if (rtt > 150 || videoLoss > 5) {
      if (this.networkMode !== 'degraded') {
        this.fallbackToDegraded();
      }
    } else if (this.networkMode !== 'normal') {
      this.restoreToNormal();
    }
  }

  fallbackToDegraded() {
    this.updateNetworkMode('degraded');
    // Reduce video quality
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.applyConstraints({
          width: { max: 640 },
          height: { max: 480 },
          frameRate: { max: 15 }
        });
      }
    }
  }

  fallbackToAudioOnly() {
    this.updateNetworkMode('audio-only');
    // Disable video
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = false;
      }
    }
    this.elements.localVideo.style.display = 'none';
    this.elements.remoteVideo.style.display = 'none';
  }

  fallbackToTranscriptOnly() {
    this.updateNetworkMode('transcript-only');
    // Disable both video and audio
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.enabled = false);
    }
    this.elements.localVideo.style.display = 'none';
    this.elements.remoteVideo.style.display = 'none';

    // Force enable transcription
    if (!this.isTranscribing) {
      this.elements.chkTranscribe.checked = true;
      this.toggleTranscription(true);
    }
  }

  restoreToNormal() {
    this.updateNetworkMode('normal');
    // Restore video and audio
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.enabled = true);
      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.applyConstraints({
          width: { max: 1280 },
          height: { max: 720 },
          frameRate: { max: 30 }
        });
      }
    }
    this.elements.localVideo.style.display = 'block';
    this.elements.remoteVideo.style.display = 'block';
  }

  // Transcription functionality
  toggleTranscription(enabled) {
    if (enabled) {
      this.startTranscription();
    } else {
      this.stopTranscription();
    }
  }

  startTranscription() {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      alert('Speech recognition not supported in this browser');
      return;
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    this.recognition = new SpeechRecognition();

    this.recognition.continuous = true;
    this.recognition.interimResults = true;
    this.recognition.lang = 'en-US';

    this.recognition.onresult = (event) => {
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      if (finalTranscript) {
        this.addTranscriptEntry('You', finalTranscript);
        // Call sentiment analysis asynchronously but don't wait for it
        this.analyzeSentiment(finalTranscript).catch(error => {
          console.error('Sentiment analysis error:', error);
        });
        this.updateEngagement('local', finalTranscript);
      }
    };

    this.recognition.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
    };

    this.recognition.onend = () => {
      if (this.isTranscribing) {
        // Restart recognition if it stops unexpectedly
        setTimeout(() => this.recognition.start(), 100);
      }
    };

    this.recognition.start();
    this.isTranscribing = true;
  }

  stopTranscription() {
    if (this.recognition) {
      this.recognition.stop();
      this.recognition = null;
    }
    this.isTranscribing = false;
  }

  addTranscriptEntry(speaker, text) {
    const timestamp = new Date().toLocaleTimeString();
    const entry = { timestamp, speaker, text };
    this.transcript.push(entry);

    const transcriptDiv = this.elements.transcript;
    const entryDiv = document.createElement('div');
    entryDiv.innerHTML = `<strong>${timestamp} - ${speaker}:</strong> ${text}`;
    transcriptDiv.appendChild(entryDiv);
    transcriptDiv.scrollTop = transcriptDiv.scrollHeight;
  }

  // Enhanced sentiment analysis using backend API
  async analyzeSentiment(text) {
    console.log('Analyzing sentiment for:', text.substring(0, 50) + '...');

    try {
      // Use advanced VADER sentiment analysis from backend
      const response = await fetch(`${this.serverUrl}/api/sentiment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: text,
          window: 5
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Sentiment analysis result:', result);

      if (result.success) {
        // Store the advanced sentiment data
        const sentimentEntry = {
          timestamp: Date.now(),
          sentiment: result.overall_sentiment,
          text: text,
          detailed: result,
          smooth_data: result.smooth_data
        };

        this.sentimentData.push(sentimentEntry);

        // Keep only last 50 entries
        if (this.sentimentData.length > 50) {
          this.sentimentData.shift();
        }

        // Update chart with smooth data
        this.updateSentimentChart();

        // Emit sentiment data to other participants
        if (this.currentRoom) {
          this.socket.emit('sentiment_analysis', {
            room: this.currentRoom,
            text: text,
            sentiment_data: result
          });
        }
      } else {
        console.error('Sentiment analysis failed:', result.error);
        // Fallback to basic sentiment analysis
        this.analyzeSentimentBasic(text);
      }
    } catch (error) {
      console.error('Error calling sentiment API:', error);
      // Fallback to basic sentiment analysis
      this.analyzeSentimentBasic(text);
    }
  }

  // Fallback basic sentiment analysis
  analyzeSentimentBasic(text) {
    console.log('Using basic sentiment analysis for:', text.substring(0, 50) + '...');

    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'like', 'happy', 'pleased', 'awesome', 'perfect', 'yes', 'agree', 'success'];
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'angry', 'frustrated', 'disappointed', 'sad', 'upset', 'no', 'disagree', 'fail', 'problem', 'difficult'];

    const words = text.toLowerCase().split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;

    words.forEach(word => {
      if (positiveWords.includes(word)) positiveCount++;
      if (negativeWords.includes(word)) negativeCount++;
    });

    let sentiment = 0; // neutral
    if (positiveCount > negativeCount) {
      sentiment = Math.min(positiveCount - negativeCount, 5) / 5; // normalize to 0-1
    } else if (negativeCount > positiveCount) {
      sentiment = -Math.min(negativeCount - positiveCount, 5) / 5; // normalize to -1-0
    }

    console.log(`Basic sentiment: ${sentiment} (pos: ${positiveCount}, neg: ${negativeCount})`);

    this.sentimentData.push({
      timestamp: Date.now(),
      sentiment: sentiment,
      text: text
    });

    // Keep only last 50 entries
    if (this.sentimentData.length > 50) {
      this.sentimentData.shift();
    }

    this.updateSentimentChart();
  }

  // Engagement tracking
  startEngagementTracking() {
    this.engagement.startTime = Date.now();
    this.engagement.participants.set('local', { speakingTime: 0, lastSpoke: null });
    this.engagement.participants.set('remote', { speakingTime: 0, lastSpoke: null });

    // Update engagement display every 5 seconds
    setInterval(() => {
      this.updateEngagementDisplay();
    }, 5000);
  }

  updateEngagement(participant, text) {
    if (!this.engagement.participants.has(participant)) {
      this.engagement.participants.set(participant, { speakingTime: 0, lastSpoke: null });
    }

    const participantData = this.engagement.participants.get(participant);
    participantData.lastSpoke = Date.now();

    // Estimate speaking time based on text length (rough approximation)
    const estimatedTime = text.split(' ').length * 0.5; // 0.5 seconds per word
    participantData.speakingTime += estimatedTime;
  }

  updateEngagementDisplay() {
    const engBarsDiv = this.elements.engBars;
    engBarsDiv.innerHTML = '';

    let maxSpeakingTime = 0;
    let champion = '';
    let silent = '';
    let minSpeakingTime = Infinity;

    this.engagement.participants.forEach((data, participant) => {
      if (data.speakingTime > maxSpeakingTime) {
        maxSpeakingTime = data.speakingTime;
        champion = participant;
      }
      if (data.speakingTime < minSpeakingTime) {
        minSpeakingTime = data.speakingTime;
        silent = participant;
      }

      const group = document.createElement('div');
      group.className = 'group';

      const label = document.createElement('div');
      label.className = 'label';
      label.textContent = `${participant}: ${Math.round(data.speakingTime)}s`;

      const bar = document.createElement('div');
      bar.className = 'bar';

      const fill = document.createElement('span');
      const percentage = maxSpeakingTime > 0 ? (data.speakingTime / maxSpeakingTime) * 100 : 0;
      fill.style.width = percentage + '%';

      bar.appendChild(fill);
      group.appendChild(label);
      group.appendChild(bar);
      engBarsDiv.appendChild(group);
    });

    this.elements.champ.textContent = `🏆 Meeting Champ: ${champion}`;
    this.elements.silent.textContent = `🤫 Silent Listener: ${silent}`;
  }

  // Sentiment chart
  initializeSentimentChart() {
    const canvas = this.elements.sentChart;
    this.sentimentChart = canvas.getContext('2d');
  }

  updateSentimentChart() {
    console.log('Updating sentiment chart with', this.sentimentData.length, 'data points');

    const canvas = this.elements.sentChart;
    const ctx = this.sentimentChart;
    const width = canvas.width;
    const height = canvas.height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    if (this.sentimentData.length === 0) {
      console.log('No sentiment data to display');
      return;
    }

    // Draw axes
    ctx.strokeStyle = '#cbd5e1';
    ctx.lineWidth = 1;

    // Y-axis (sentiment scale)
    ctx.beginPath();
    ctx.moveTo(30, 10);
    ctx.lineTo(30, height - 20);
    ctx.stroke();

    // X-axis (time)
    ctx.beginPath();
    ctx.moveTo(30, height / 2);
    ctx.lineTo(width - 10, height / 2);
    ctx.stroke();

    // Draw threshold lines
    ctx.strokeStyle = '#22c55e';
    ctx.lineWidth = 1;
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(30, height / 2 - (0.05 * (height / 2 - 20)));
    ctx.lineTo(width - 10, height / 2 - (0.05 * (height / 2 - 20)));
    ctx.stroke();

    ctx.strokeStyle = '#ef4444';
    ctx.beginPath();
    ctx.moveTo(30, height / 2 - (-0.05 * (height / 2 - 20)));
    ctx.lineTo(width - 10, height / 2 - (-0.05 * (height / 2 - 20)));
    ctx.stroke();
    ctx.setLineDash([]);

    // Draw sentiment line - use smooth data if available
    if (this.sentimentData.length > 0) {
      const latestEntry = this.sentimentData[this.sentimentData.length - 1];

      if (latestEntry.smooth_data && latestEntry.smooth_data.x && latestEntry.smooth_data.y) {
        // Draw smooth interpolated curve
        ctx.strokeStyle = '#3b82f6';
        ctx.lineWidth = 2.5;
        ctx.beginPath();

        const smoothX = latestEntry.smooth_data.x;
        const smoothY = latestEntry.smooth_data.y;
        const xScale = (width - 40) / (smoothX.length - 1);

        smoothX.forEach((_, index) => {
          const x = 30 + index * xScale;
          const y = height / 2 - (smoothY[index] * (height / 2 - 20));

          if (index === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        });

        ctx.stroke();

        // Draw actual data points
        ctx.fillStyle = '#f97316';
        if (latestEntry.detailed && latestEntry.detailed.scores) {
          const scores = latestEntry.detailed.scores;
          const pointXScale = (width - 40) / (scores.length - 1);

          scores.forEach((score, index) => {
            const x = 30 + index * pointXScale;
            const y = height / 2 - (score * (height / 2 - 20));

            ctx.beginPath();
            ctx.arc(x, y, 4, 0, 2 * Math.PI);
            ctx.fill();
          });
        }
      } else {
        // Fallback to basic line chart
        ctx.strokeStyle = '#0ea5e9';
        ctx.lineWidth = 2;
        ctx.beginPath();

        const xStep = (width - 40) / (this.sentimentData.length - 1);

        this.sentimentData.forEach((point, index) => {
          const x = 30 + index * xStep;
          const y = height / 2 - (point.sentiment * (height / 2 - 20));

          if (index === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        });

        ctx.stroke();
      }
    }

    // Draw labels
    ctx.fillStyle = '#475569';
    ctx.font = '12px system-ui';
    ctx.fillText('Positive', 5, 20);
    ctx.fillText('Neutral', 5, height / 2);
    ctx.fillText('Negative', 5, height - 10);

    // Draw legend
    ctx.font = '10px system-ui';
    ctx.fillStyle = '#3b82f6';
    ctx.fillText('Smooth Trend', width - 100, 20);
    ctx.fillStyle = '#f97316';
    ctx.fillText('Data Points', width - 100, 35);
  }

  // Simple attention score display
  updateAttentionDisplay(attentionData) {
    // Update current attention score
    const currentScore = (attentionData.attention_score * 100).toFixed(0);
    this.elements.currentAttention.textContent = `🎯 Attention: ${currentScore}%`;

    // Update badge color based on attention level
    const badge = this.elements.currentAttention;
    badge.className = 'badge';
    if (attentionData.attention_score >= 0.7) {
      badge.style.backgroundColor = '#22c55e'; // Green for high attention
    } else if (attentionData.attention_score >= 0.4) {
      badge.style.backgroundColor = '#f59e0b'; // Orange for medium attention
    } else {
      badge.style.backgroundColor = '#ef4444'; // Red for low attention
    }

    // Show/hide low attention alert
    if (attentionData.attention_score < 0.3 || attentionData.drowsy_alert) {
      this.elements.attentionAlert.style.display = 'inline-block';
      this.elements.attentionAlert.textContent = attentionData.drowsy_alert ? '😴 Drowsy Alert!' : '⚠️ Low Attention!';
    } else {
      this.elements.attentionAlert.style.display = 'none';
    }
  }

  // Simple automatic attention tracking
  startAttentionTracking() {
    if (this.isAttentionTracking || !this.localStream) return;

    this.isAttentionTracking = true;
    console.log('Attention tracking started automatically');

    // Start simple attention monitoring
    this.startSimpleAttentionMonitoring();
  }

  stopAttentionTracking() {
    if (!this.isAttentionTracking) return;

    this.isAttentionTracking = false;

    // Stop attention monitoring
    if (this.attentionInterval) {
      clearInterval(this.attentionInterval);
      this.attentionInterval = null;
    }

    console.log('Attention tracking stopped');
  }

  // Simple attention monitoring based on activity
  startSimpleAttentionMonitoring() {
    let lastActivity = Date.now();
    let attentionScore = 0.8; // Start with good attention

    // Monitor user activity (mouse movement, keyboard, speaking, scrolling)
    const updateActivity = () => {
      lastActivity = Date.now();
      // Boost attention score immediately on activity
      attentionScore = Math.min(1.0, attentionScore + 0.1);
    };

    // Add comprehensive activity listeners for real-time detection
    document.addEventListener('mousemove', updateActivity);
    document.addEventListener('keypress', updateActivity);
    document.addEventListener('keydown', updateActivity);
    document.addEventListener('click', updateActivity);
    document.addEventListener('scroll', updateActivity);
    document.addEventListener('touchstart', updateActivity);
    document.addEventListener('touchmove', updateActivity);

    // Monitor speaking activity
    if (this.recognition) {
      this.recognition.addEventListener('result', updateActivity);
    }

    // Update attention score every 500ms for real-time responsiveness
    this.attentionInterval = setInterval(() => {
      if (!this.isAttentionTracking) return;

      const timeSinceActivity = Date.now() - lastActivity;

      // Calculate attention based on recent activity (more responsive)
      if (timeSinceActivity < 2000) { // Active in last 2 seconds
        attentionScore = Math.min(1.0, attentionScore + 0.05);
      } else if (timeSinceActivity < 5000) { // Active in last 5 seconds
        attentionScore = Math.max(0.4, attentionScore - 0.02);
      } else if (timeSinceActivity < 10000) { // Active in last 10 seconds
        attentionScore = Math.max(0.2, attentionScore - 0.03);
      } else { // No activity for 10+ seconds
        attentionScore = Math.max(0.1, attentionScore - 0.05);
      }

      // Add subtle randomness for realism
      attentionScore += (Math.random() - 0.5) * 0.02;
      attentionScore = Math.max(0.1, Math.min(1.0, attentionScore));

      // Update display
      const attentionData = {
        attention_score: attentionScore,
        drowsy_alert: attentionScore < 0.2,
        timestamp: Date.now()
      };

      this.updateAttentionDisplay(attentionData);

    }, 500); // Update every 500ms for real-time feel
  }



  // AI-powered MOM (Minutes of Meeting) generation
  async generateMOM() {
    if (this.transcript.length === 0) {
      alert('No transcript available to summarize');
      return;
    }

    // Show loading indicator
    this.showMOMLoading(true);

    try {
      // Prepare transcript text for AI analysis
      const fullTranscript = this.transcript.map(entry =>
        `${entry.speaker}: ${entry.text}`
      ).join('\n');

      // Call AI summary API
      const response = await fetch(`${this.serverUrl}/api/summarize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transcript: fullTranscript
        })
      });

      const result = await response.json();

      if (result.success) {
        // Parse AI-generated summary
        this.parseAndDisplayAISummary(result.summary);
      } else {
        console.error('AI summary failed:', result.error);
        // Fallback to basic MOM generation
        this.generateBasicMOM();
      }
    } catch (error) {
      console.error('Error calling AI summary API:', error);
      // Fallback to basic MOM generation
      this.generateBasicMOM();
    } finally {
      this.showMOMLoading(false);
    }
  }

  // Fallback basic MOM generation
  generateBasicMOM() {
    const fullText = this.transcript.map(entry => entry.text).join(' ');

    // Extract key topics (simplified - look for repeated keywords)
    const words = fullText.toLowerCase().split(/\s+/);
    const wordCount = {};
    const stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they'];

    words.forEach(word => {
      if (word.length > 3 && !stopWords.includes(word)) {
        wordCount[word] = (wordCount[word] || 0) + 1;
      }
    });

    // Get top keywords
    const topKeywords = Object.entries(wordCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word);

    // Generate basic MOM items
    const momItems = [
      `Meeting Duration: ${this.getMeetingDuration()}`,
      `Participants: ${Array.from(this.engagement.participants.keys()).join(', ')}`,
      `Key Topics Discussed: ${topKeywords.join(', ')}`,
      `Total Speaking Time: ${this.getTotalSpeakingTime()}s`
    ];

    // Extract action items (look for action words)
    const actionWords = ['will', 'should', 'need to', 'must', 'action', 'todo', 'task', 'assign', 'responsible'];
    const actionItems = [];

    this.transcript.forEach(entry => {
      const text = entry.text.toLowerCase();
      actionWords.forEach(actionWord => {
        if (text.includes(actionWord)) {
          const sentences = entry.text.split(/[.!?]+/);
          sentences.forEach(sentence => {
            if (sentence.toLowerCase().includes(actionWord)) {
              actionItems.push(sentence.trim());
            }
          });
        }
      });
    });

    // Update UI with basic MOM
    this.updateMOMDisplay(momItems);
    this.updateActionItemsDisplay([...new Set(actionItems)]);
  }

  // Parse and display AI-generated summary
  parseAndDisplayAISummary(aiSummary) {
    const sections = aiSummary.split(/(?=📋|📝|✅)/);
    let summaryText = '';
    let minutesItems = [];
    let actionItems = [];

    sections.forEach(section => {
      if (section.includes('📋 Summary:')) {
        summaryText = section.replace('📋 Summary:', '').trim();
      } else if (section.includes('📝 Meeting Minutes:')) {
        const minutesText = section.replace('📝 Meeting Minutes:', '').trim();
        minutesItems = minutesText.split('\n')
          .filter(line => line.trim().startsWith('-'))
          .map(line => line.replace(/^-\s*/, '').trim())
          .filter(item => item.length > 0);
      } else if (section.includes('✅ Action Items:')) {
        const actionsText = section.replace('✅ Action Items:', '').trim();
        actionItems = actionsText.split('\n')
          .filter(line => line.trim().startsWith('-'))
          .map(line => line.replace(/^-\s*/, '').trim())
          .filter(item => item.length > 0);
      }
    });

    // Add meeting metadata
    const enhancedMomItems = [
      `📋 AI Summary: ${summaryText}`,
      `⏱️ Meeting Duration: ${this.getMeetingDuration()}`,
      `👥 Participants: ${Array.from(this.engagement.participants.keys()).join(', ')}`,
      `🗣️ Total Speaking Time: ${this.getTotalSpeakingTime()}s`,
      ...minutesItems
    ];

    // Update UI with AI-generated content
    this.updateMOMDisplay(enhancedMomItems);
    this.updateActionItemsDisplay(actionItems);
  }

  // Show/hide loading indicator for MOM generation
  showMOMLoading(show) {
    const momList = this.elements.mom;
    const actionsList = this.elements.actions;

    if (show) {
      momList.innerHTML = '<li>🤖 Generating AI-powered summary...</li>';
      actionsList.innerHTML = '<li>🔄 Analyzing for action items...</li>';
    }
  }

  updateMOMDisplay(items) {
    const momList = this.elements.mom;
    momList.innerHTML = '';

    items.forEach(item => {
      const li = document.createElement('li');
      li.textContent = item;
      momList.appendChild(li);
    });
  }

  updateActionItemsDisplay(items) {
    const actionsList = this.elements.actions;
    actionsList.innerHTML = '';

    items.forEach(item => {
      const li = document.createElement('li');
      li.textContent = item;
      actionsList.appendChild(li);
    });
  }

  getMeetingDuration() {
    if (!this.engagement.startTime) return '0 minutes';
    const duration = Math.round((Date.now() - this.engagement.startTime) / 60000);
    return `${duration} minutes`;
  }

  getTotalSpeakingTime() {
    let total = 0;
    this.engagement.participants.forEach(data => {
      total += data.speakingTime;
    });
    return Math.round(total);
  }

  // Recording functionality
  startRecording() {
    if (!this.localStream) {
      alert('No local stream available for recording');
      return;
    }

    try {
      // Create a combined stream with both local and remote audio/video
      const combinedStream = new MediaStream();

      // Add local tracks
      this.localStream.getTracks().forEach(track => {
        combinedStream.addTrack(track);
      });

      // Add remote tracks if available
      if (this.remoteStream) {
        this.remoteStream.getTracks().forEach(track => {
          combinedStream.addTrack(track);
        });
      }

      this.mediaRecorder = new MediaRecorder(combinedStream, {
        mimeType: 'video/webm;codecs=vp9'
      });

      this.recordedChunks = [];

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);
        this.elements.playback.src = url;

        // Create download link
        const a = document.createElement('a');
        a.href = url;
        a.download = `meeting-recording-${new Date().toISOString().slice(0, 19)}.webm`;
        a.textContent = 'Download Recording';
        a.style.display = 'block';
        a.style.marginTop = '10px';

        // Add download link after playback video
        this.elements.playback.parentNode.insertBefore(a, this.elements.playback.nextSibling);
      };

      this.mediaRecorder.start(1000); // Collect data every second
      this.isRecording = true;

      this.elements.btnRecStart.disabled = true;
      this.elements.btnRecStop.disabled = false;

      console.log('Recording started');
    } catch (error) {
      console.error('Error starting recording:', error);
      alert('Error starting recording: ' + error.message);
    }
  }

  stopRecording() {
    if (this.mediaRecorder && this.isRecording) {
      this.mediaRecorder.stop();
      this.isRecording = false;

      this.elements.btnRecStart.disabled = false;
      this.elements.btnRecStop.disabled = true;

      console.log('Recording stopped');
    }
  }
}

// Initialize the application
const meetingTwin = new AdaptiveMeetingTwin();

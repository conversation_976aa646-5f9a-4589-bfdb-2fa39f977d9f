@echo off
echo ================================================================
echo    Adaptive Meeting Twin - Integrated AI System Launcher
echo ================================================================
echo.
echo Features:
echo  ✓ Enhanced Signaling Server with AI APIs
echo  ✓ Gemini AI Meeting Summaries
echo  ✓ VADER Sentiment Analysis with Smooth Graphs
echo  ✓ Real-time Attention Tracking
echo  ✓ WebRTC Video Conferencing
echo.
echo ================================================================

REM Check if we're in the correct directory
if not exist "working_server.py" (
    echo ERROR: working_server.py not found!
    echo Please run this script from the project root directory.
    pause
    exit /b 1
)

if not exist "frontend\index.html" (
    echo ERROR: frontend\index.html not found!
    echo Please ensure the frontend directory exists.
    pause
    exit /b 1
)

echo [1/4] Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again.
    pause
    exit /b 1
)

echo [2/4] Checking required Python packages...
python -c "import flask, flask_socketio, google.generativeai, nltk, cv2, mediapipe" >nul 2>&1
if errorlevel 1 (
    echo WARNING: Some required packages may be missing.
    echo Installing required packages...
    pip install flask flask-socketio google-generativeai nltk scipy opencv-python mediapipe numpy
    if errorlevel 1 (
        echo ERROR: Failed to install required packages.
        echo Please install manually: pip install flask flask-socketio google-generativeai nltk scipy opencv-python mediapipe numpy
        pause
        exit /b 1
    )
)

echo [3/4] Starting Integrated HTTP Server...
echo.
echo Checking for port conflicts...
netstat -ano | findstr :6000 >nul
if not errorlevel 1 (
    echo ⚠️ Port 6000 is in use. Attempting to free it...
    call cleanup_port.bat
    timeout /t 2 /nobreak >nul
)

echo Server will be available at:
echo   Localhost: http://localhost:6000 (or alternative port)
echo   Network:   Check server console for network IP
echo.
echo API Endpoints:
echo   - POST /api/summarize     (AI Meeting Summary)
echo   - POST /api/sentiment     (Advanced Sentiment Analysis)
echo   - POST /api/attention/*   (Attention Tracking)
echo.

REM Start the integrated server in a new window
start "Adaptive Meeting Twin - Integrated Server" cmd /k "python working_server.py"

REM Wait for the server to start
timeout /t 5 /nobreak >nul

echo [4/4] Opening Application...
echo.
echo Application will open in your default browser via HTTP server.
echo This fixes file:// protocol issues and enables network access.
echo.

REM Open the application via HTTP server
start "" "http://localhost:6000"

echo ================================================================
echo                    System Started Successfully!
echo ================================================================
echo.
echo Instructions:
echo 1. The integrated server is running in a separate window
echo 2. The application is now open in your browser via HTTP
echo 3. Enter a room name and click "Join Room" to start
echo 4. Transcription starts automatically (no button needed)
echo 5. Attention tracking starts automatically (real-time score)
echo 6. Click "Generate Summary" for AI-powered meeting minutes
echo 7. Share network URL with others for multi-device access
echo.
echo To stop the system:
echo - Close the backend server window
echo - Close the browser tab
echo.
echo Troubleshooting:
echo - If the backend fails to start, check the server window for errors
echo - Ensure your camera/microphone permissions are granted
echo - For attention tracking, make sure your camera is working
echo.
echo ================================================================

pause
